root = true

[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true

[*.{kt,kts}]
indent_size = 4
continuation_indent_size = 4
max_line_length = 120

[*.{xml,json,yml,yaml}]
indent_size = 2

[*.md]
trim_trailing_whitespace = false

# Kotlin specific rules
[*.{kt,kts}]
ktlint_standard_no-wildcard-imports = disabled
ktlint_standard_max-line-length = disabled
ktlint_standard_import-ordering = disabled
