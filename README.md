**利用Android无障碍服务（AccessibilityService）能做什么**

# Assists作用

基于Android无障碍服务（AccessibilityService）封装的框架

1. 简化自动化脚本开发
2. 为自动化脚本提供各种增强能力
3. 提高脚本易维护性
4. 支持html+js/vue开发自动化脚本

# 主要能力

1. 易于使用的无障碍服务API
2. 浮窗管理器：易于实现及管理浮窗
3. 步骤器：为快速实现、可复用、易维护的自动化步骤提供框架及管理
4. 配套屏幕管理：快速生成输出屏幕截图、元素截图
5. 屏幕管理结合opencv：便于屏幕内容识别为自动化提供服务
6. 封装webview接口支持html+js/vue开发自动化脚本


# 🎉新增JS支持库
新增支持通过Web端实现Android平台自动化脚本的JS库：**[assistsx-js](https://github.com/ven-coder/assistsx-js)**

# 🚀 快速开始

### 1. 导入依赖

#### 1.1 项目根目录build.gradle添加

```
allprojects {
    repositories {
	//添加jitpack仓库
        maven { url 'https://jitpack.io' }
    }
}
```

#### 1.2 主模块build.gradle添加

最新版本：[![](https://jitpack.io/v/ven-coder/Assists.svg)](https://jitpack.io/#ven-coder/Assists)

```
dependencies {
    //按需添加
    //基础库（必须）
    implementation "com.github.ven-coder.Assists:assists-base:最新版本"
    //屏幕录制相关（可选）
    implementation "com.github.ven-coder.Assists:assists-mp:最新版本"
    //opencv相关（可选）
    implementation "com.github.ven-coder.Assists:assists-opcv:最新版本"
    //html+js/vue自动化脚本支持（可选，⚠️当前还处于可用但非稳定版本）
    implementation "com.github.ven-coder.Assists:assists-web:0.0.21-SNAPSHOT"
  
}
```

### 2. 注册&开启服务

#### 1.1 主模块AndroidManifest.xml中注册服务

一定要在主模块中注册服务，不然进程被杀服务也会自动被关闭需要再次开启（小米可保持杀进程保持开启，其他vivo、oppo、鸿蒙机型似乎不行）

```xml
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.ven.assists.simple">

    <application
        android:name="com.ven.assists.simple.App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true">
        <!-- 添加代码 ↓-->
        <service
            android:name="com.ven.assists.service.AssistsService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <!--android:priority="10000" 可提高服务在设置中的权重，排在前面-->
            <intent-filter android:priority="10000">
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/assists_service" />
        </service>
      
        <!-- 或者使用下面的服务可以解决一些应用混淆节点的问题，比如微信8.0.51以上版本获取的节点元素错乱问题 -->
        <!-- ⚠️ 选其一 -->
        <service
            android:name="com.google.android.accessibility.selecttospeak.SelectToSpeakService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <!--android:priority="10000" 可提高服务在设置中的权重，排在前面     -->
            <intent-filter android:priority="10000">
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/assists_service" />
        </service>
      
        <!-- 添加代码 ↑-->
    </application>

</manifest>
```

#### 1.2 开启服务

调用 ``AssistsCore.openAccessibilitySetting()``跳转到无障碍服务设置页面，找到对应的应用开启服务。
服务开启后执行以下API测试是否成功集成：

```
AssistsCore.getAllNodes().forEach { it.logNode() }
```

这段代码是获取当前页面所有节点元素的基本信息在Logcat（tag：assists_log）打印出来，如下图：

<img src="https://github.com/user-attachments/assets/81725dc3-d924-44f4-89fe-75938ae659e9" width=350/>

至此，已成功集成Assists。如果没有任何输出请检查集成步骤是否正确。

# 步骤器-快速实现复杂自动化脚本

步骤器可以帮助快速实现复杂的业务场景，比如自动发朋友圈、获取微信所有好友昵称、自动删除好友...等等都是一些逻辑较多的业务场景，步骤器可帮助快速实现。

### 1.继承 ``StepImpl``

直接在接口 `onImpl(collector: StepCollector)`写步骤逻辑，每个步骤自定义步骤的序号，用于区分执行的步骤。如果重复则会以最后一个步骤为准

```kotlin
class MyStepImpl:StepImpl() {
    override fun onImpl(collector: StepCollector) {
	//定义步骤序号为1的逻辑
        collector.next(1) {// 1为步骤的序号
            //步骤1逻辑
	    ...
            //返回下一步需要执行的序号，通过Step.get([序号])，如果需要重复该步骤可返回Step.repeat，如果返回Step.none则不执行任何步骤，相当于停止
            return@next Step.get(2, delay = 1000) //将会执行步骤2逻辑
        }.next(2) {
            //步骤2逻辑
	    ...
	    //返回下一步需要执行的序号，通过Step.get([序号])
	    return@next Step.get(3)
        }.next(3) {
            //步骤3逻辑
	    ...
	    //返回下一步需要执行的序号，通过Step.get([序号])
	    return@next Step.get(4)
        }
	其他步骤
	...
    }
}

```

### 2. 开始执行

执行前请确保无障碍服务已开启

```kotlin
//从MyStepImpl步骤1开始执行，isBegin是否作为起始步骤，默认false
StepManager.execute(MyStepImpl::class.java, 1, isBegin = true)
```

### 3. 停止执行

```kotlin
// 设置停止标志，将取消所有正在执行的步骤
StepManager.isStop = true
```

## API列表

### 初始化和服务管理

| 方法名 | 说明 | 返回值 |
|--------|------|--------|
| `init(application: Application)` | 初始化AssistsCore | 无 |
| `openAccessibilitySetting()` | 打开系统无障碍服务设置页面 | 无 |
| `isAccessibilityServiceEnabled()` | 检查无障碍服务是否已开启 | Boolean |
| `getPackageName()` | 获取当前窗口所属的应用包名 | String |

### 元素查找

| 方法名 | 说明 | 返回值 |
|--------|------|--------|
| `findById(id, filterText?, filterDes?, filterClass?)` | 通过id查找所有符合条件的元素 | List<AccessibilityNodeInfo> |
| `AccessibilityNodeInfo.findById(id, filterText?, filterDes?, filterClass?)` | 在指定元素范围内通过id查找元素 | List<AccessibilityNodeInfo> |
| `findByText(text, filterViewId?, filterDes?, filterClass?)` | 通过文本内容查找所有符合条件的元素 | List<AccessibilityNodeInfo> |
| `findByTextAllMatch(text, filterViewId?, filterDes?, filterClass?)` | 查找所有文本完全匹配的元素 | List<AccessibilityNodeInfo> |
| `AccessibilityNodeInfo.findByText(text, filterViewId?, filterDes?, filterClass?)` | 在指定元素范围内通过文本查找元素 | List<AccessibilityNodeInfo> |
| `findByTags(className, viewId?, text?, des?)` | 根据多个条件查找元素 | List<AccessibilityNodeInfo> |
| `AccessibilityNodeInfo.findByTags(className, viewId?, text?, des?)` | 在指定元素范围内根据多个条件查找元素 | List<AccessibilityNodeInfo> |
| `getAllNodes(filterViewId?, filterDes?, filterClass?, filterText?)` | 获取当前窗口中的所有元素 | List<AccessibilityNodeInfo> |

### 元素信息获取

| 方法名 | 说明 | 返回值 |
|--------|------|--------|
| `AccessibilityNodeInfo.txt()` | 获取元素的文本内容 | String |
| `AccessibilityNodeInfo.des()` | 获取元素的描述内容 | String |
| `AccessibilityNodeInfo.getAllText()` | 获取元素的所有文本内容（包括text和contentDescription） | ArrayList<String> |
| `AccessibilityNodeInfo.containsText(text)` | 判断元素是否包含指定文本 | Boolean |
| `AccessibilityNodeInfo.getBoundsInScreen()` | 获取元素在屏幕中的位置信息 | Rect |
| `AccessibilityNodeInfo.getBoundsInParent()` | 获取元素在父容器中的位置信息 | Rect |
| `AccessibilityNodeInfo.isVisible(compareNode?, isFullyByCompareNode?)` | 判断元素是否可见 | Boolean |

### 元素层级操作

| 方法名 | 说明 | 返回值 |
|--------|------|--------|
| `AccessibilityNodeInfo.getNodes()` | 获取指定元素下的所有子元素 | ArrayList<AccessibilityNodeInfo> |
| `AccessibilityNodeInfo.getChildren()` | 获取元素的直接子元素 | ArrayList<AccessibilityNodeInfo> |
| `AccessibilityNodeInfo.findFirstParentByTags(className)` | 查找第一个符合指定类型的父元素 | AccessibilityNodeInfo? |
| `AccessibilityNodeInfo.findFirstParentClickable()` | 查找元素的第一个可点击的父元素 | AccessibilityNodeInfo? |

### 元素操作

| 方法名 | 说明 | 返回值 |
|--------|------|--------|
| `AccessibilityNodeInfo.click()` | 点击元素 | Boolean |
| `AccessibilityNodeInfo.longClick()` | 长按元素 | Boolean |
| `AccessibilityNodeInfo.paste(text)` | 向元素粘贴文本 | Boolean |
| `AccessibilityNodeInfo.setNodeText(text)` | 设置元素的文本内容 | Boolean |
| `AccessibilityNodeInfo.selectionText(selectionStart, selectionEnd)` | 选择元素中的文本 | Boolean |
| `AccessibilityNodeInfo.scrollForward()` | 向前滚动可滚动元素 | Boolean |
| `AccessibilityNodeInfo.scrollBackward()` | 向后滚动可滚动元素 | Boolean |

### [更多API](https://github.com/ven-coder/Assists/blob/master/API_REFERENCE.md)

## 示例教程

- [Appium结合AccessibilityService实现自动化微信登录](https://juejin.cn/post/7483409317564907530)

## 其他教程博客

### 获取节点信息

- [使用weditor获取节点信息](https://juejin.cn/post/7484188555735613492)
- [使用Appium获取节点信息](https://juejin.cn/post/7483409317564907530)
- [使用uiautomatorviewer获取节点信息](https://blog.csdn.net/weixin_37496178/article/details/138328871?fromshare=blogdetail&sharetype=blogdetail&sharerId=138328871&sharerefer=PC&sharesource=weixin_37496178&sharefrom=from_link)


# License

[GNU General Public License v3.0](https://github.com/ven-coder/Assists/blob/master/LICENSE)


打包simple的apk
PS E:\code\Assists> ./gradlew :simple:assembleDebug

启动服务端：
PS E:\code\Assists> $env:JAVA_HOME="C:\Program Files\Android\Android Studio\jbr"
PS E:\code\Assists> $OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding;
PS E:\code\Assists> ./gradlew :conch-server:bootRun