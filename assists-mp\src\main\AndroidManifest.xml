<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ven.assists.mp">

    <application>
        <provider
            android:name=".MPFileProvider"
            android:authorities="${applicationId}.assistsmp.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/assists_mp_provider_paths" />
        </provider>
        <service
            android:name=".MPService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

    </application>

</manifest>