package com.ven.assists.web

object CallMethod {
    const val setNodeText = "setNodeText"
    const val findByTags = "findByTags"
    const val findById = "findById"
    const val findByText = "findByText"
    const val findByTextAllMatch = "findByTextAllMatch"
    const val containsText = "containsText"
    const val getAllText = "getAllText"
    const val findFirstParentByTags = "findFirstParentByTags"
    const val getAllNodes = "getAllNodes"
    const val getNodes = "getNodes"
    const val findFirstParentClickable = "findFirstParentClickable"
    const val getChildren = "getChildren"
    const val dispatchGesture = "dispatchGesture"
    const val getBoundsInScreen = "getBoundsInScreen"
    const val getBoundsInParent = "getBoundsInParent"
    const val isVisible = "isVisible"
    const val click = "click"
    const val longClick = "longClick"
    const val gestureClick = "gestureClick"
    const val back = "back"
    const val home = "home"
    const val notifications = "notifications"
    const val recentApps = "recentApps"
    const val paste = "paste"
    const val selectionText = "selectionText"
    const val scrollForward = "scrollForward"
    const val scrollBackward = "scrollBackward"
    const val launchApp = "launchApp"
    const val getPackageName = "getPackageName"
    const val overlayToast = "overlayToast"
    const val getScreenSize = "getScreenSize"
    const val getAppScreenSize = "getAppScreenSize"
    const val nodeGestureClick = "nodeGestureClick"
    const val nodeGestureClickByDouble = "nodeGestureClickByDouble"
    const val takeScreenshot = "takeScreenshot"
    const val setOverlayFlags = "setOverlayFlags"
    const val scanQR = "scanQR"
    const val addWebFloatingWindow = "addWebFloatingWindow"
    const val getLocalDomain = "getLocalDomain"
}