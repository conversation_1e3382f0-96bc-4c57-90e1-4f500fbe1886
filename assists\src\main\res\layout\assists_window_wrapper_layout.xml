<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="#000000">

    <LinearLayout
        android:id="@+id/fl_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_move"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="right"
            android:padding="10dp"
            android:src="@drawable/move" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="18dp"
            tools:text="Assists" />

        <ImageView
            android:id="@+id/iv_minimize"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="right"
            android:padding="12dp"
            android:src="@drawable/minimize"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="right"
            android:padding="12dp"
            android:src="@drawable/icon_close" />

    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <ImageView
        android:id="@+id/iv_scale"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="bottom"
        android:padding="10dp"
        android:src="@drawable/scale" />

</LinearLayout>