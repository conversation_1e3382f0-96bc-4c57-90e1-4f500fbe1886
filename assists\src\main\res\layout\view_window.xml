<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80000000"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_move"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="right"
                android:padding="10dp"
                android:src="@drawable/move" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="center"
                android:text="日志"
                android:textColor="@android:color/white"
                android:textSize="18dp" />

            <ImageView
                android:id="@+id/iv_minimize"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="right"
                android:visibility="gone"
                android:padding="12dp"
                android:src="@drawable/minimize" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="right"
                android:padding="12dp"
                android:src="@drawable/icon_close" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#898989" />

        <FrameLayout
            android:id="@+id/fl_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <ImageView
            android:id="@+id/iv_scale"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="bottom"
            android:padding="10dp"
            android:src="@drawable/scale" />

    </LinearLayout>


</FrameLayout>