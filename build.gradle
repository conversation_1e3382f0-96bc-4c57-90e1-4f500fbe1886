// Top-level build file where you can add configuration options common to all sub-projects/modules.


buildscript {
    ext {
        // SDK 版本
        compileSdk = 34
        minSdk = 24
        targetSdk = 33

        // 应用版本
        versionCode = 11
        versionName = "3.2.172"
        mavenVersion = "3.2.172"

        // 依赖库版本
        coreKtxVersion = "1.13.1"
        activityKtxVersion = "1.9.0"
        appcompatVersion = "1.6.1"
        kotlinBomVersion = "1.9.22"
        utilcodexVersion = "1.31.1"
        opencvVersion = "4.9.0"

        // Room
        roomVersion = "2.4.3"

        // UI相关
        constraintlayoutVersion = "2.1.4"
        materialVersion = "1.8.0"
        recyclerviewVersion = "1.3.0"
        xpopupVersion = "2.9.17"
        flexboxVersion = "3.0.0"

        // 网络相关
        ok2curlVersion = "0.8.0"
        okgoVersion = "3.0.4"
        okhttpLoggingVersion = "4.10.0"

        // 图片加载
        glideVersion = "4.15.1"

        // Gradle 插件版本
        gradleVersion = "8.0.2"
        kotlinGradleVersion = "1.9.22"
        dokkaVersion = "1.8.10"
    }
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://jitpack.io' }
        mavenCentral()
        google()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.22"
        classpath "com.google.dagger:hilt-android-gradle-plugin:2.44"
        classpath 'org.springframework.boot:spring-boot-gradle-plugin:3.2.2'
        classpath "org.jetbrains.kotlin:kotlin-allopen:1.9.22"

        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
    }
}
plugins {
    id 'org.jetbrains.dokka' version '1.8.10'
    id 'org.jlleitschuh.gradle.ktlint' version '11.6.1'
}
allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://jitpack.io' }
        mavenCentral()
        google()
    }

    // 应用ktlint到所有子项目
    apply plugin: 'org.jlleitschuh.gradle.ktlint'

    ktlint {
        version = "0.50.0"
        debug = false
        verbose = false
        android = true
        outputToConsole = true
        outputColorName = "RED"
        ignoreFailures = false
        enableExperimentalRules = false

        filter {
            exclude("**/generated/**")
            include("**/kotlin/**")
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
