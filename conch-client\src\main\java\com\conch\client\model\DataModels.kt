package com.conch.client.model

/**
 * 指令请求数据模型
 */
data class CommandRequest(
    val text: String,
    val confidence: Double = 1.0,
    val timestamp: Long = System.currentTimeMillis(),
    val deviceInfo: DeviceInfo,
)

/**
 * 设备信息数据模型
 */
data class DeviceInfo(
    val model: String,
    val androidVersion: String,
    val screenResolution: String,
    val installedApps: List<String> = emptyList(),
)

/**
 * 指令响应数据模型
 */
data class CommandResponse(
    val sessionId: String,
    val state: String,
    val message: String,
    val estimatedDuration: Long,
)

/**
 * 执行进度数据模型
 */
data class ExecutionProgress(
    val percentage: Float,
    val currentStep: String,
    val steps: List<ExecutionStep>,
)

/**
 * 执行步骤数据模型
 */
data class ExecutionStep(
    val description: String,
    val status: StepStatus,
)

/**
 * 步骤状态枚举
 */
enum class StepStatus {
    PENDING, // 待执行
    EXECUTING, // 执行中
    COMPLETED, // 已完成
    FAILED, // 失败
}
