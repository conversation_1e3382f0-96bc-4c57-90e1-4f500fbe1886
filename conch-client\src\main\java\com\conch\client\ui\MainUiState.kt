package com.conch.client.ui

import androidx.compose.runtime.Stable

/**
 * 主界面UI状态管理
 */
@Stable
data class MainUiState(
    val inputText: String = "",
    val isProcessing: Boolean = false,
    val isExecuting: Boolean = false,
    val executionProgress: Float = 0f,
    val networkStatus: String = "未知",
    val executionLogs: List<String> = emptyList(),
) {
    /**
     * 添加执行日志，限制最大数量防止内存泄漏
     */
    fun addLog(message: String, maxLogs: Int = 50): MainUiState {
        val newLogs = (executionLogs + message).takeLast(maxLogs)
        return copy(executionLogs = newLogs)
    }

    /**
     * 清空日志
     */
    fun clearLogs(): MainUiState {
        return copy(executionLogs = emptyList())
    }

    /**
     * 开始处理
     */
    fun startProcessing(): MainUiState {
        return copy(
            isProcessing = true,
            isExecuting = true,
            executionProgress = 0f,
        )
    }

    /**
     * 完成处理
     */
    fun completeProcessing(): MainUiState {
        return copy(
            isProcessing = false,
            isExecuting = false,
            executionProgress = 1f,
        )
    }

    /**
     * 更新进度
     */
    fun updateProgress(progress: Float): MainUiState {
        return copy(executionProgress = progress.coerceIn(0f, 1f))
    }

    /**
     * 更新网络状态
     */
    fun updateNetworkStatus(status: String): MainUiState {
        return copy(networkStatus = status)
    }

    /**
     * 更新输入文本
     */
    fun updateInputText(text: String): MainUiState {
        return copy(inputText = text)
    }
}
