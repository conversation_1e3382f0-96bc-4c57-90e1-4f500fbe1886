apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'

group = 'com.conch'
version = '1.0.0'
sourceCompatibility = '17'

repositories {
    maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    maven { url "https://maven.aliyun.com/repository/public" }
    maven { url 'https://repo1.maven.org/maven2/' }
    maven { url 'https://jitpack.io' }
    mavenCentral()
    google()
}

dependencies {
    // Spring Boot - 使用版本目录
    implementation libs.bundles.spring.boot

    // Kotlin
    implementation libs.kotlin.reflect
    implementation libs.kotlin.stdlib
    implementation libs.kotlinx.coroutines.reactor

    // 开发工具
    developmentOnly libs.spring.boot.devtools

    // 测试
    testImplementation libs.bundles.server.test
    testRuntimeOnly libs.junit.platform.launcher
}

tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict']
        jvmTarget = '17'
    }
}

tasks.named('test') {
    useJUnitPlatform()
}
