package com.conch.server.util

import org.slf4j.LoggerFactory
import java.io.PrintStream
import java.nio.charset.StandardCharsets

/**
 * 日志输出工具类
 * 解决中文乱码问题
 */
object LogUtil {
    
    private val logger = LoggerFactory.getLogger(LogUtil::class.java)
    
    /**
     * 输出指令日志（使用英文避免编码问题）
     */
    fun logCommand(
        action: String,
        deviceModel: String,
        command: String,
        sessionId: String,
        timestamp: String
    ) {
        // 使用英文日志，避免编码问题
        logger.info("=".repeat(60))
        logger.info("[COMMAND RECEIVED] New user command")
        logger.info("Device Model: {}", deviceModel)
        logger.info("User Command: \"{}\"", command)
        logger.info("Session ID: {}", sessionId)
        logger.info("Received Time: {}", timestamp)
        logger.info("=".repeat(60))
    }
    
    /**
     * 输出脚本生成日志（使用英文避免编码问题）
     */
    fun logScriptGeneration(sessionId: String, scriptId: String, actionCount: Int) {
        logger.info("[SCRIPT GENERATION] Generated execution script for session {}", sessionId)
        logger.info("Script ID: {}", scriptId)
        logger.info("Action Count: {}", actionCount)
        logger.info("Generation Time: {}", java.time.LocalDateTime.now())
    }
    
    /**
     * 输出反馈日志（使用英文避免编码问题）
     */
    fun logFeedback(
        sessionId: String,
        scriptId: String,
        status: String,
        executionTime: Long,
        completedActions: Int,
        totalActions: Int
    ) {
        logger.info("[EXECUTION FEEDBACK] Received client execution feedback")
        logger.info("Session ID: {}", sessionId)
        logger.info("Script ID: {}", scriptId)
        logger.info("Execution Status: {}", status)
        logger.info("Execution Time: {}ms", executionTime)
        logger.info("Completed Actions: {}/{}", completedActions, totalActions)
        logger.info("-".repeat(40))
    }
}
