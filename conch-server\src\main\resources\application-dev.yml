# 开发环境配置
server:
  port: 8080

conch:
  # 开发环境会话配置
  session:
    timeout: 600000  # 10分钟 - 开发时更长的超时时间
    max-concurrent: 10  # 开发环境限制并发数
    cleanup-interval: 30000  # 30秒清理间隔
  
  # 开发环境脚本配置
  script:
    max-actions: 20  # 开发环境限制动作数量
    default-timeout: 10000  # 更长的超时时间便于调试
    max-retry-count: 1  # 减少重试次数便于调试
  
  # 开发环境反馈配置
  feedback:
    image-max-size: 1048576  # 1MB - 开发环境较小的图片限制
    retention-days: 7  # 开发环境保留7天

logging:
  level:
    com.conch: DEBUG
    org.springframework.web: DEBUG
    org.springframework.boot: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 开发环境特定配置
debug: true
