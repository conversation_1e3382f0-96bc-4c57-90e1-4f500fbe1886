# 生产环境配置
server:
  port: ${SERVER_PORT:8080}

conch:
  # 生产环境会话配置
  session:
    timeout: 300000  # 5分钟
    max-concurrent: 1000  # 生产环境支持更多并发
    cleanup-interval: 60000  # 1分钟清理间隔
  
  # 生产环境脚本配置
  script:
    max-actions: 100  # 生产环境支持更多动作
    default-timeout: 5000  # 标准超时时间
    max-retry-count: 3  # 标准重试次数
  
  # 生产环境反馈配置
  feedback:
    image-max-size: 5242880  # 5MB - 生产环境更大的图片限制
    retention-days: 90  # 生产环境保留90天

logging:
  level:
    com.conch: INFO
    org.springframework.web: WARN
    org.springframework.boot: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/conch-server.log
    max-size: 100MB
    max-history: 30

# 生产环境特定配置
debug: false

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: never
