server:
  port: ${SERVER_PORT:8080}
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: conch-server
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# 应用配置
conch:
  server:
    name: "小田螺助手服务端"
    version: "1.0.0"
    description: "智能手机自动化助手服务端"

  # 会话配置
  session:
    timeout: ${SESSION_TIMEOUT:300000}  # 5分钟
    max-concurrent: ${MAX_CONCURRENT_SESSIONS:100}
    cleanup-interval: ${SESSION_CLEANUP_INTERVAL:60000}  # 1分钟

  # 脚本执行配置
  script:
    max-actions: ${MAX_SCRIPT_ACTIONS:50}
    default-timeout: ${DEFAULT_ACTION_TIMEOUT:5000}
    max-retry-count: ${MAX_RETRY_COUNT:3}

  # 反馈处理配置
  feedback:
    image-max-size: ${FEEDBACK_IMAGE_MAX_SIZE:2097152}  # 2MB
    retention-days: ${FEEDBACK_RETENTION_DAYS:30}

logging:
  level:
    com.conch: ${LOG_LEVEL_CONCH:INFO}
    org.springframework.web: ${LOG_LEVEL_SPRING_WEB:WARN}
    org.springframework.boot: ${LOG_LEVEL_SPRING_BOOT:INFO}
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  charset:
    console: UTF-8

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
