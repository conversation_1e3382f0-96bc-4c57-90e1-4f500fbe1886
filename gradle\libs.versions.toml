[versions]
# Android SDK 版本
compileSdk = "34"
minSdk = "24"
targetSdk = "33"

# 应用版本
versionCode = "11"
versionName = "3.2.172"
mavenVersion = "3.2.172"

# Ko<PERSON>in 和 Gradle
kotlin = "1.9.22"
gradle = "8.2.2"
dokka = "1.8.10"

# Android 核心库
coreKtx = "1.13.1"
activityKtx = "1.9.0"
appcompat = "1.6.1"
lifecycle = "2.7.0"
activity-compose = "1.8.2"

# Compose
compose-bom = "2024.02.00"
compose-compiler = "1.5.10"

# UI 相关
constraintlayout = "2.1.4"
material = "1.8.0"
recyclerview = "1.3.0"
xpopup = "2.9.17"
flexbox = "3.0.0"

# 网络相关
ok2curl = "0.8.0"
okgo = "3.0.4"
okhttp-logging = "4.10.0"
retrofit = "2.9.0"

# 图片加载
glide = "4.15.1"

# 数据库
room = "2.4.3"

# 工具库
utilcodex = "1.31.1"
opencv = "4.9.0"

# 依赖注入
hilt = "2.44"

# Spring Boot (服务端)
spring-boot = "3.2.2"

# 测试
junit = "4.13.2"
junit-ext = "1.1.5"
espresso = "3.5.1"
kotlin-test = "1.9.22"

[libraries]
# Android 核心
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activity-compose" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }

# Compose BOM
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "compose-bom" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }

# Material Design
material = { group = "com.google.android.material", name = "material", version.ref = "material" }

# Kotlin
kotlin-stdlib = { group = "org.jetbrains.kotlin", name = "kotlin-stdlib", version.ref = "kotlin" }
kotlin-reflect = { group = "org.jetbrains.kotlin", name = "kotlin-reflect", version.ref = "kotlin" }
kotlinx-coroutines-reactor = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-reactor" }

# 网络
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp-logging" }
okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp-logging" }

# 依赖注入
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }

# 数据库
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }

# Spring Boot (服务端)
spring-boot-starter-web = { group = "org.springframework.boot", name = "spring-boot-starter-web" }
spring-boot-starter-websocket = { group = "org.springframework.boot", name = "spring-boot-starter-websocket" }
spring-boot-starter-validation = { group = "org.springframework.boot", name = "spring-boot-starter-validation" }
spring-boot-devtools = { group = "org.springframework.boot", name = "spring-boot-devtools" }
jackson-module-kotlin = { group = "com.fasterxml.jackson.module", name = "jackson-module-kotlin" }
jackson-datatype-jsr310 = { group = "com.fasterxml.jackson.datatype", name = "jackson-datatype-jsr310" }

# 测试
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junit-ext" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espresso" }
spring-boot-starter-test = { group = "org.springframework.boot", name = "spring-boot-starter-test" }
kotlin-test-junit5 = { group = "org.jetbrains.kotlin", name = "kotlin-test-junit5", version.ref = "kotlin-test" }
junit-platform-launcher = { group = "org.junit.platform", name = "junit-platform-launcher" }

[plugins]
android-application = { id = "com.android.application", version.ref = "gradle" }
android-library = { id = "com.android.library", version.ref = "gradle" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spring-dependency-management = { id = "io.spring.dependency-management", version = "1.1.4" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
dokka = { id = "org.jetbrains.dokka", version.ref = "dokka" }

[bundles]
# Android 核心包
android-core = [
    "androidx-core-ktx",
    "androidx-lifecycle-runtime-ktx",
    "androidx-appcompat",
    "material"
]

# Compose 包
compose = [
    "androidx-compose-ui",
    "androidx-compose-ui-tooling-preview",
    "androidx-compose-material3"
]

# 网络包
network = [
    "retrofit",
    "retrofit-converter-gson",
    "okhttp",
    "okhttp-logging"
]

# 数据库包
room = [
    "room-runtime",
    "room-ktx"
]

# Spring Boot 包
spring-boot = [
    "spring-boot-starter-web",
    "spring-boot-starter-websocket",
    "spring-boot-starter-validation",
    "jackson-module-kotlin",
    "jackson-datatype-jsr310"
]

# 测试包
test = [
    "junit",
    "androidx-junit",
    "androidx-espresso-core"
]

# 服务端测试包
server-test = [
    "spring-boot-starter-test",
    "kotlin-test-junit5"
]
