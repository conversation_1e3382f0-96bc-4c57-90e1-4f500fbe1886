<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_enable"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/colorAccent"
            android:text="开启服务" />

        <LinearLayout
            android:id="@+id/ll_option"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

            <Button
                android:id="@+id/btn_basic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="基础" />

            <Button
                android:id="@+id/btn_pro"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="进阶" />

            <Button
                android:id="@+id/btn_advanced"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="高级" />

            <Button
                android:id="@+id/btn_web"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="@color/colorAccent"
                android:text="Web支持"
                android:textAllCaps="false" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>