<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/parent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="#4D000000">

    <LinearLayout
        android:id="@+id/ll_log"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_log"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="5dp"
                    android:textColor="@color/_ffffff"
                    android:textSize="10dp"
                    tools:text="aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa" />
            </FrameLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_stop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="停止"
                android:textSize="12dp" />

            <Button
                android:id="@+id/btn_clean"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="清除"
                android:textSize="12dp" />

            <TextView
                android:id="@+id/tv_length"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0"
                android:textColor="#ffffff"
                android:textSize="12dp"
                android:visibility="gone" />

        </LinearLayout>
    </LinearLayout>

</LinearLayout>