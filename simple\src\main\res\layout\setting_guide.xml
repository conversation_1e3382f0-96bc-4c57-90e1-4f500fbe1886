<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:clickable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            android:background="@drawable/bg_3" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#ffffff"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="15dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="20dp"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginBottom="20dp"
                    android:text="Assists开启引导"
                    android:textColor="#000000"
                    android:textSize="18sp"
                    android:textStyle="bold" />


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="#009688"
                        android:gravity="center"
                        android:text="1"

                        android:textColor="#ffffff"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:text="下滑找到"

                        android:textColor="#000000"
                        android:textSize="16sp" />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="【已安装的服务】"

                        android:textColor="#009688"
                        android:textSize="16sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="#009688"
                        android:gravity="center"

                        android:text="2"
                        android:textColor="#ffffff"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"

                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:text="下滑找到"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"

                        android:layout_height="wrap_content"
                        android:text="【AssistsSimple】"
                        android:textColor="#009688"
                        android:textSize="16sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:background="#009688"
                        android:gravity="center"

                        android:text="3"
                        android:textColor="#ffffff"
                        android:textSize="16sp" />

                    <TextView
                        android:layout_width="wrap_content"

                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:text="点击打开功能开关"
                        android:textColor="#000000"
                        android:textSize="16sp" />


                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:background="@drawable/bg_2"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_marginStart="10dp"
                        android:src="@drawable/icon_1" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="AssistsSimple"
                        android:textColor="#000000"
                        android:textSize="16sp" />

                    <FrameLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <ImageView
                            android:layout_width="60dp"
                            android:layout_height="40dp"
                            android:src="@drawable/icon_2" />


                    </FrameLayout>

                </LinearLayout>

            </LinearLayout>

            <ImageView
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_gravity="bottom|end"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="10dp"
                android:src="@drawable/icon_3" />

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="right"
                android:padding="10dp"
                android:src="@drawable/icon_4" />

        </FrameLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>