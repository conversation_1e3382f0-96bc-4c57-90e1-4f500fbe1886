<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/parent"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_1"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/ll_option"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">


                    <Button
                        android:id="@+id/btn_setting_guide"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="无障碍服务开启引导示例"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_notification"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="通知监听（包括Toast）"
                        android:textAllCaps="false"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_1"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="禁止下拉通知栏"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="自动接听微信电话"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_open_social"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="自动打开微信朋友圈"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_publish_social"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="自动发表一条微信私密朋友圈"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_scroll_contacts"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="自动滚动微信通讯录"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_click_bottom_tab"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="手势点击微信主页底部Tab"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:id="@+id/btn_scroll_social"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="手势滑动朋友圈列表"
                        android:textColor="@color/colorPrimary" />

                    <Button
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="自动抢红包"
                        android:textColor="@color/colorPrimary"
                        android:visibility="gone" />

                    <Button
                        android:id="@+id/btn_ant_forest_energy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/colorAccent"
                        android:text="蚂蚁森林能量自动收集"
                        android:textColor="@color/colorPrimary" />


                </LinearLayout>
            </androidx.core.widget.NestedScrollView>


            <Button
                android:id="@+id/btn_log"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:backgroundTint="#ccc000"
                android:text="查看日志"
                android:textColor="@color/colorPrimary" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_log"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="【日志】"
                    android:textColor="@color/_ffffff" />

                <Button
                    android:id="@+id/btn_stop_scroll_log"
                    android:layout_width="65dp"
                    android:layout_height="35dp"
                    android:backgroundTint="#FF9800"
                    android:text="停止滚动"
                    android:textColor="@color/_ffffff"
                    android:textSize="10dp" />
            </LinearLayout>

            <androidx.core.widget.NestedScrollView
                android:id="@+id/scrollView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:minHeight="250dp">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_log"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="5dp"
                        android:text=""
                        android:textColor="@color/_ffffff" />
                </FrameLayout>
            </androidx.core.widget.NestedScrollView>

            <Button
                android:id="@+id/btn_stop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="#E02626"
                android:text="停止"
                android:textColor="@color/_ffffff" />

            <Button
                android:id="@+id/btn_close_log"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:backgroundTint="#4CAF50"
                android:text="关闭日志"
                android:textColor="@color/_ffffff" />
        </LinearLayout>

    </LinearLayout>

</FrameLayout>