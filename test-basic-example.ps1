# Test Basic Example Functions

# Set UTF-8 encoding
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding

Write-Host "Testing basic example functions..." -ForegroundColor Green

# Test back command
$backCommand = @{
    textCommand = @{
        text = "back"
        confidence = 1.0
        timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()
    }
    deviceInfo = @{
        model = "Test Device"
        androidVersion = "Android 13"
        screenResolution = "1080x2400"
        installedApps = @()
    }
} | ConvertTo-Json -Depth 3

Write-Host "Sending back command..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/voice/command" -Method POST -Body $backCommand -ContentType "application/json"
    Write-Host "Back command sent successfully: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Send failed: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 2

# Test home command
$homeCommand = @{
    textCommand = @{
        text = "home"
        confidence = 1.0
        timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()
    }
    deviceInfo = @{
        model = "Test Device"
        androidVersion = "Android 13"
        screenResolution = "1080x2400"
        installedApps = @()
    }
} | ConvertTo-Json -Depth 3

Write-Host "Sending home command..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/voice/command" -Method POST -Body $homeCommand -ContentType "application/json"
    Write-Host "Home command sent successfully: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Send failed: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 2

# Test show notifications command
$notificationCommand = @{
    textCommand = @{
        text = "show_notifications"
        confidence = 1.0
        timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()
    }
    deviceInfo = @{
        model = "Test Device"
        androidVersion = "Android 13"
        screenResolution = "1080x2400"
        installedApps = @()
    }
} | ConvertTo-Json -Depth 3

Write-Host "Sending show notifications command..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/voice/command" -Method POST -Body $notificationCommand -ContentType "application/json"
    Write-Host "Show notifications command sent successfully: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Send failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed! Please check server logs to confirm commands were received." -ForegroundColor Green
