# Test single command to verify server logs

# Set UTF-8 encoding
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding

Write-Host "Testing single command to verify server logs..." -ForegroundColor Green

# Test back command
$backCommand = @{
    textCommand = @{
        text = "back"
        confidence = 1.0
        timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()
    }
    deviceInfo = @{
        model = "Test Device"
        androidVersion = "Android 13"
        screenResolution = "1080x2400"
        installedApps = @()
    }
} | ConvertTo-Json -Depth 3

Write-Host "Sending back command..." -ForegroundColor Yellow
Write-Host "Command JSON: $backCommand" -ForegroundColor Cyan

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/voice/command" -Method POST -Body $backCommand -ContentType "application/json"
    Write-Host "Back command sent successfully: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "Send failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed! Please check server terminal for logs." -ForegroundColor Green
