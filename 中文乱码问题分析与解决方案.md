# 中文乱码问题分析与解决方案

## 🔍 问题根本原因分析

### 1. 乱码现象
- **控制台输出乱码**: `锟斤拷` 等典型UTF-8→GBK乱码
- **指令内容显示为**: `????` 或乱码
- **英文和数字正常**: 说明基础通信正常

### 2. 根本原因
这是一个**多层编码问题**：

#### 层次1: 终端/控制台编码问题
- **现象**: `[指锟斤拷锟斤拷锟絔 锟秸碉拷锟矫伙拷指锟斤拷`
- **原因**: PowerShell/CMD使用GBK编码，而源码是UTF-8
- **影响**: 仅影响显示，不影响功能

#### 层次2: 数据传输编码问题  
- **现象**: 指令内容显示为 `????`
- **原因**: JSON数据在传输过程中编码转换问题
- **影响**: 影响实际功能

最终解决方案：
$OutputEncoding = [console]::InputEncoding = [console]::OutputEncoding = New-Object System.Text.UTF8Encoding; ./gradlew :conch-server:bootRun
太棒了 解决了
